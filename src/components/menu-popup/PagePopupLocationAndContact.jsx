import { ACTIONS_EXPERIENCE_STATE } from '@/contexts/reducerExperience'
import React, { useState } from 'react'
// import {APIProvider, Map} from '@vis.gl/react-google-maps';
import { useContextExperience } from '@/contexts/useContextExperience';
import HtmlContentDisplay from '@/components/HtmlContentDisplay'
import { HiX } from 'react-icons/hi';
import ImageWrapperResponsive from '@/components/ImageWrapperResponsive'
import { settings } from '@/lib/settings'

function BtnLandingpageComponent({data,fn,index}) {
    const [swap,setSwap]=useState(true)
    // console.log('BtnLandingpageComponent:',settings?.locatioSubmitBtn)
    return(
        <div 
            onMouseEnter={()=>setSwap(!swap)} 
            onMouseLeave={()=>setSwap(!swap)} 
            className='btn-wrapper flex relative items-center justify-center cursor-pointer max-w-fit max-h-fit'
        >
            <div 
                // onClick={fn?.[index]} 
                className={`${swap ? 'hidden' : 'flex'} top-0 left-0 w-fit h-fit`}
            >
                <ImageWrapperResponsive className={'w-auto h-full'} alt='button images for landpage options' src={settings?.locatioSubmitBtn?.btnIcons?.ov}/>
            </div>
            <div
                // onClick={fn?.[index]} 
                className={`${swap ? 'flex' : 'hidden'} top-0 left-0 w-fit h-fit`}
            >
                <ImageWrapperResponsive className={'w-auto h-full'} alt='button images for landpage options' src={settings?.locatioSubmitBtn?.btnIcons?.off}/>
            </div>
        </div>
    )
}

export default function PagePopupLocationAndContact({data}) {
  const {experienceState,disptachExperience}=useContextExperience()

  // Form state management
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    message: ''
  });
  const [isLoading, setIsLoading] = useState(false);
  const [submitStatus, setSubmitStatus] = useState(null); // 'success', 'error', or null
  const [errorMessage, setErrorMessage] = useState('');
  const [successMessage, setSuccessMessage] = useState('');

  // Form handlers
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));

    // Clear error messages when user starts typing
    if (submitStatus === 'error') {
      setSubmitStatus(null);
      setErrorMessage('');
    }
  };

  const validateForm = () => {
    if (!formData.name.trim()) {
      setErrorMessage('Name is required');
      return false;
    }

    if (!formData.email.trim()) {
      setErrorMessage('Email is required');
      return false;
    }

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(formData.email)) {
      setErrorMessage('Please enter a valid email address');
      return false;
    }

    if (!formData.message.trim()) {
      setErrorMessage('Message is required');
      return false;
    }

    if (formData.message.trim().length < 10) {
      setErrorMessage('Message must be at least 10 characters long');
      return false;
    }

    return true;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) {
      setSubmitStatus('error');
      return;
    }

    setIsLoading(true);
    setSubmitStatus(null);
    setErrorMessage('');
    setSuccessMessage('');

    try {
      const response = await fetch('/api/contact', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      const result = await response.json();

      if (result.success) {
        setSubmitStatus('success');
        setSuccessMessage(result.message);
        // Reset form
        setFormData({
          name: '',
          email: '',
          message: ''
        });
      } else {
        setSubmitStatus('error');
        setErrorMessage(result.message || 'Failed to send message. Please try again.');
      }
    } catch (error) {
      console.error('Error submitting contact form:', error);
      setSubmitStatus('error');
      setErrorMessage('Network error. Please check your connection and try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handlePopupClose=()=>{
    // console.log('handlePopupCLose',experienceState?.showTheIslandPage)
    // console.log('handlePopupCLose',experienceState?.showExperiencePage)
    {experienceState?.showLocationAndContacts && disptachExperience({type:ACTIONS_EXPERIENCE_STATE.SHOW_LOCATION_AND_CONTACTS_PAGE})}
  }
  // const {image,body,title,secondaryEntries,url}=item
  const locationGPS={lat:-24.6424504,lng:25.8965668}
  const containerStyles={with:'100%',height:'100%'}
  const textList=[
    {title:'WHATSAPP',desc:'(+267) 72 808 308'},
    {title:'TELEPHONE',desc:'(+34) 72 808 308'},
    {title:'EMAIL',desc:'<EMAIL>'},
    {title:'ADDRESS',desc:`Boro River, Okavango,
            Maun, Botswana`
    },
    {title:'GPS CO-ORDINATES',desc:`19, 51'25, 2468 | S
            23, 26'7, 6056 | E`
    },
  ]
  const inputList=[
    {name:'name',type:'text'},
    {name:'email',type:'email'},
  ]
  
  // console.log(data)
  return (
    <div className='popu-location-and-contactsflex z-30 absolute top-0 left-0 w-full h-full bg-black/85 overflow-hidden overflow-y-auto'>
      <div className='popu-island-experiences flex z-30 absolute top-0 left-0 w-full h-full overflow-hidden overflow-y-auto text-white items-center justify-center'>
        <div className='popup-wrapper text-white text-4xl flex z-10 absolute top-0 left-0 w-full h-full overflow-y-auto'>
          <div 
            onClick={handlePopupClose} 
            className=" flex z-40 items-center justify-center absolute right-0 top-[0] h-[75px] w-fit cursor-pointer"
          >
            <HiX className='mr-5'/>
            {/* <div className='w-fit h-fit invisible'><_360BookNowBtn/></div> */}
          </div>
          <div className='flex overflow-hidden relative top-[75px] left-0 h-fit px-5 q-full mx-auto flex-col text-white mb-40'>
            <a 
              href='https://www.google.com/maps/place/Elephant+Island+Botswana/@-19.8569273,23.4327953,1072m/data=!3m2!1e3!4b1!4m6!3m5!1s0x19544be9d2377447:0xe3aebe0bd1973684!8m2!3d-19.8569324!4d23.4353702!16s%2Fg%2F11xtd_hlv4?entry=ttu&g_ep=EgoyMDI1MDgwNi4wIKXMDSoASAFQAw%3D%3D' 
              className='flex font-thin w-full h-auto items-center justify-center overflow-hidden'
              target="_blank"
              rel="noopener noreferrer"
            >
              <img src={'/assets/map_location_img_001.jpg'} alt='page image' className='object-cover h-auto w-full'/>
              {/* <APIProvider apiKey={process.env.GOOGLE_API_KEY}>
                <Map
                  style={{width: '100%', height: '100%'}}
                  defaultCenter={{lat: locationGPS.lat, lng: locationGPS.lng}}
                  defaultZoom={3}
                  gestureHandling={'greedy'}
                  disableDefaultUI={true}
                />
              </APIProvider> */}
            </a>
            <div className='flex mx-auto mt-7 max-w-full lg:max-w-[676px] h-full gap-10 flex-col lg:flex-row'>
              <div className='flex w-1/3 flex-col'>
                {/* LOCATION AND CONTACTS TITLE */}
                <div className='w-full text-3xl leading-10 font-bold text-left text-wrap uppercase mb-2 text-[40px]'>
                  <HtmlContentDisplay htmlString={data?.title}/>
                </div>

                {/* LOCATION AND CONTACTS DETAILS */}
                <div className='w-full'>
                  <HtmlContentDisplay htmlString={data?.details}/>
                </div>
              </div>
              <div className='flex flex-col w-full'>
                {/* LOCATION AND CONTACTS BODY TEXT */}
                <div className='mb-4'>
                  <HtmlContentDisplay htmlString={data?.body}/>
                </div>

                {/* INPUT FORM FOR CONTACT SECTION */}
                <form className='flex mt-1 flex-col w-full gap-3' onSubmit={handleSubmit}>
                  {/* Success Message */}
                  {submitStatus === 'success' && (
                    <div className='bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded-xl mb-2'>
                      <p className='text-sm'>{successMessage}</p>
                    </div>
                  )}

                  {/* Error Message */}
                  {submitStatus === 'error' && (
                    <div className='bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-xl mb-2'>
                      <p className='text-sm'>{errorMessage}</p>
                    </div>
                  )}

                  {/* Name Field */}
                  <div className='flex flex-col w-full gap-1'>
                    <span className='text-sm uppercase text-white'>Name</span>
                    <input
                      name='name'
                      value={formData.name}
                      onChange={handleInputChange}
                      className='w-full h-10 px-3 text-sm py-2 border-6 border-white rounded-xl outline-none text-white'
                      // placeholder='Enter your full name'
                      type="text"
                      disabled={isLoading}
                      required
                    />
                  </div>

                  {/* Email Field */}
                  <div className='flex flex-col w-full gap-1'>
                    <span className='text-sm uppercase text-white'>Email</span>
                    <input
                      name='email'
                      value={formData.email}
                      onChange={handleInputChange}
                      className='w-full h-10 px-3 text-sm py-2 border-6 border-white rounded-xl outline-none text-white'
                      // placeholder='Enter your email address'
                      type="email"
                      disabled={isLoading}
                      required
                    />
                  </div>

                  {/* Message Field */}
                  <div className='flex flex-col w-full gap-1'>
                    <span className='text-sm uppercase text-white'>Message</span>
                    <textarea
                      name='message'
                      value={formData.message}
                      onChange={handleInputChange}
                      rows={6}
                      className='w-full h-32 px-3 text-sm py-2 border-6 border-white rounded-xl outline-none text-white resize-none'
                      // placeholder='Enter your message (minimum 10 characters)'
                      disabled={isLoading}
                      required
                      minLength={10}
                    />
                  </div>

                  {/* Submit Button */}
                  {/* <button
                    type="submit"
                    disabled={isLoading}
                    className={`flex items-center justify-center w-fit px-6 py-2 rounded-full mt-2 outline-none transition-all duration-200 ${
                      isLoading
                        ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                        : 'bg-white text-gray-900 hover:bg-gray-100 cursor-pointer'
                    }`}
                  >
                    {isLoading ? (
                      <>
                        <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Sending...
                      </>
                    ) : (
                      'Send Message'
                    )}
                  </button> */}
                  <BtnLandingpageComponent data={settings}/>
                  
                </form>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
